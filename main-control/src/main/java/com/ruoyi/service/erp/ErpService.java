package com.ruoyi.service.erp;

import com.google.gson.reflect.TypeToken;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.domain.bill.DeliveryNotice;
import com.ruoyi.domain.bill.DeliveryNoticeDetail;
import com.ruoyi.domain.bill.ReceiveNotice;
import com.ruoyi.domain.bill.ReceiveNoticeDetail;
import com.ruoyi.domain.erp.ErpReportDetail;
import com.ruoyi.domain.erp.ErpReportMain;
import com.ruoyi.service.bill.DeliveryNoticeDetailService;
import com.ruoyi.service.bill.DeliveryNoticeService;
import com.ruoyi.service.bill.ReceiveNoticeService;
import com.ruoyi.service.bill.ReceiveNoticeDetailService;
import com.ruoyi.service.sys.ProjectSysConfigService;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.GsonUtils;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.erp.common.*;
import com.ruoyi.vo.erp.otherstock.*;
import com.ruoyi.vo.erp.purchasestock.*;
import com.ruoyi.vo.erp.push.PushRequest;
import com.ruoyi.vo.erp.response.ErpResponse;
import com.ruoyi.vo.erp.response.PushResponse;
import kingdee.bos.webapi.client.K3CloudApiClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author:lhb
 * @create: 2023-09-06 16:14
 * @Description: 对接ERP服务层
 */
@Service
public class ErpService {

    private K3CloudApiClient k3CloudApiClient;
    private static final Logger logger = LoggerFactory.getLogger(ErpService.class);

    @Resource
    private ProjectSysConfigService projectSysConfigService;

    @Resource
    private DeliveryNoticeService deliveryNoticeService;

    @Resource
    private DeliveryNoticeDetailService deliveryNoticeDetailService;

    @Resource
    private ReceiveNoticeService receiveNoticeService;

    @Resource
    private ReceiveNoticeDetailService receiveNoticeDetailService;

    /**
     * 获取K3CloudApiClient实例
     */
    private K3CloudApiClient getK3CloudApiClient() {
        if (k3CloudApiClient == null) {
            String erpUrl = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.ERP_URL);
            if (erpUrl == null || erpUrl.trim().isEmpty()) {
                logger.error("未配置ERP URL，请在project_sys_config表中配置ERP_URL");
                throw new CustomException("ERP URL未配置");
            }
            logger.info("初始化ERP客户端，URL: {}", erpUrl);
            k3CloudApiClient = new K3CloudApiClient(erpUrl);
        }
        return k3CloudApiClient;
    }

    /**
     * 登录erp
     */
    public boolean loginErp() {
        try {
            Boolean login = getK3CloudApiClient().login(
                    projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.DB_ID),
                    projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.USER_NAME),
                    projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.PASSWORD), 2052);
            if (login != null && login) {
                return true;
            }
        } catch (Exception e) {
            logger.error("ERP登录时发生异常: {}", e.getMessage());
        }
        logger.error("ERP登录失败!");
        return false;
    }

    /**
     * 查询ERP单据
     *
     * @param queryContextName 查询上下文名称 (e.g., "物料信息")
     * @param json      请求的JSON体
     * @return 响应的JSON字符串
     */
    public String queryErpBillInfo(String queryContextName, String json) {
        logger.info("开始查询ERP[{}], 请求内容: {}", queryContextName, json);
        Object erpResponseObject;
        try {
            erpResponseObject = getK3CloudApiClient().executeBillQuery(json);
        } catch (Exception e) {
            logger.error("查询ERP[{}]时发生异常: {}", queryContextName, e.getMessage());
            return null;
        }
        String queryResponse = GsonUtils.toJsonString(erpResponseObject);
        // 为了避免日志过大，默认不打印完整的响应体，调试时可以打开
        // logger.debug("查询ERP[{}]完成, 响应内容: {}", queryContextName, queryResponse);
        return queryResponse;
    }

    /**
     * 通用的ERP列表数据获取方法
     * @param queryReq 查询请求
     * @param queryContextName 查询上下文名称，用于日志记录
     * @return 列表
     */
    public List<Map<String, Object>> getErpDataList(ErpQueryReq queryReq, String queryContextName) throws Exception {
        if (!loginErp()) {
            // loginErp方法内部已经记录了详细的错误日志
            return Collections.emptyList();
        }

        String resultJson = this.queryErpBillInfo(queryContextName, GsonUtils.toJsonString(queryReq));

        if (StringUtils.isEmpty(resultJson) || !resultJson.trim().startsWith("[")) {
            logger.error("[ERP服务-{}] 调用失败或返回格式不正确。响应: {}", queryContextName, resultJson);
            return Collections.emptyList();
        }

        List<List<Object>> rawList;
        try {
            rawList = GsonUtils.fromJson(resultJson, new TypeToken<List<List<Object>>>() {}.getType());
        } catch (Exception e) {
            logger.error("[ERP服务-{}] JSON解析失败: {}，响应: {}", queryContextName, e.getMessage(), resultJson);
            return Collections.emptyList();
        }

        if (rawList == null || rawList.isEmpty()) {
            logger.info("[{}]查询结果为空，无需同步。", queryContextName);
            return Collections.emptyList();
        }

        String[] fieldKeys = queryReq.getFieldKeys().split(",");
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (List<Object> row : rawList) {
            Map<String, Object> map = new HashMap<>();
            for (int i = 0; i < fieldKeys.length; i++) {
                if (i < row.size()) {
                    Object value = row.get(i);
                    // 数据清洗：将整数型的Double转为Long
                    if (value instanceof Double && ((Double) value) % 1 == 0) {
                        value = ((Double) value).longValue();
                    }
                    map.put(fieldKeys[i], value);
                }
            }
            resultList.add(map);
        }
        logger.info("成功将[{}]的{}条数据转换为Map列表。", queryContextName, resultList.size());
        return resultList;
    }

    /**
     * 保存ERP单据
     *
     * @param form_id    表单id
     * @param form_name  表单名称
     * @param json       保存内容
     * @param signalType 信号类型
     * @return 单据编号
     */
    public String sendSavaBill(String form_id, String form_name, String json, Integer signalType) {
        String saveResponse = null;
        try {
            saveResponse = k3CloudApiClient.save(form_id, json);
        } catch (Exception e) {
            logger.error("ERP保存单据异常: {}", e.getMessage());
            return ""; // 异常时直接返回空字符串
        }
        // 检查响应是否为空
        if (saveResponse == null || saveResponse.trim().isEmpty()) {
            logger.error("ERP保存单据响应为空");
            return "";
        }
        logger.info("上报ERP" + form_name + " 保存,请求内容:" + json + "；响应内容:" + saveResponse);
        try {
            ErpResponse savaErpResponse = GsonUtils.gsonToBean(saveResponse, ErpResponse.class);
            // 添加空值检查，避免空指针异常
            if (savaErpResponse != null &&
                savaErpResponse.getResult() != null &&
                savaErpResponse.getResult().getResponseStatus() != null &&
                savaErpResponse.getResult().getResponseStatus().getIsSuccess()) {
                return savaErpResponse.getResult().getNumber();
            }
        } catch (Exception e) {
            logger.error("解析ERP响应异常: {}", e.getMessage());
        }

        return "";
    }

    /**
     * 提交ERP单据
     *
     * @param form_id    表单id
     * @param form_name  表单名称
     * @param bill       保存内容
     * @param signalType 信号类型
     * @return 单据编号
     */
    public boolean sendSubmitSavaBill(String form_id, String form_name, String bill, Integer signalType) {
        // STK_TransferDirect
        String saveResponse = null;
        try {
            SendYwOpter auDitSendYwOpter = new SendYwOpter();
            auDitSendYwOpter.setNumbers(new String[] { bill });
            saveResponse = k3CloudApiClient.submit(form_id, GsonUtils.toJsonString(auDitSendYwOpter));
        } catch (Exception e) {
            logger.error("ERP提交单据异常: {}", e.getMessage());
        }
        logger.info("上报ERP" + form_name + " 提交请求单据编号:" + bill + "；响应内容:" + saveResponse);
        ErpResponse savaErpResponse = GsonUtils.gsonToBean(saveResponse, ErpResponse.class);
        return savaErpResponse.getResult().getResponseStatus().getIsSuccess();
    }

    /**
     * 审核ERP单据
     *
     * @param form_id    表单id
     * @param form_name  表单名称
     * @param bill       单据编号
     * @param signalType 信号类型
     * @return 是否成功
     */
    public boolean sendAuditSavaBill(String form_id, String form_name, String bill, Integer signalType) {
        String saveResponse = null;
        try {
            SendYwOpter auDitSendYwOpter = new SendYwOpter();
            auDitSendYwOpter.setNumbers(new String[] { bill });
            saveResponse = k3CloudApiClient.audit(form_id, GsonUtils.toJsonString(auDitSendYwOpter));
        } catch (Exception e) {
            logger.error("ERP审核单据异常: {}", e.getMessage());
        }
        logger.info("上报ERP" + form_name + " 审核提交单据编号:" + bill + "；响应内容:" + saveResponse);
        ErpResponse savaErpResponse = GsonUtils.gsonToBean(saveResponse, ErpResponse.class);
        return savaErpResponse.getResult().getResponseStatus().getIsSuccess();
    }

    /**
     * 下推ERP单据
     *
     * @param sourceFormId 源单据FormId
     * @param pushRequest  下推请求参数
     * @return 响应结果
     */
    public String pushBill(String sourceFormId, PushRequest pushRequest) throws Exception {
        logger.info("开始下推ERP单据，源表单ID: {}, 下推参数: {}", sourceFormId, GsonUtils.toJsonString(pushRequest));
        String response = null;
        try {
            String requestData = GsonUtils.toJsonString(pushRequest);
            response = (String) getK3CloudApiClient().execute("Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Push",
                    new Object[]{sourceFormId, requestData},String.class);
            logger.info("下推ERP单据完成，源表单ID: {}, 响应内容: {}", sourceFormId, response);
        } catch (Exception e) {
            logger.error("下推ERP单据异常，源表单ID: {}, 错误: {}", sourceFormId, e.getMessage());
            throw e;
        }
        return response;
    }

    /**
     * 采购订单下推采购入库单（完整流程：下推→提交→审核）
     *
     * @param purchaseOrderNumbers 采购订单编码列表
     * @return 下推结果：true-成功，false-失败
     */
    public boolean pushPurchaseOrderToInStock(List<String> purchaseOrderNumbers) {
        logger.info("开始采购订单下推采购入库单完整流程，采购订单编码: {}", purchaseOrderNumbers);

        if (purchaseOrderNumbers == null || purchaseOrderNumbers.isEmpty()) {
            logger.error("采购订单下推失败: 采购订单编码列表不能为空");
            return false;
        }

        try {
            // 1. 登录ERP
            if (!loginErp()) {
                logger.error("采购订单下推失败: ERP登录失败");
                return false;
            }

            // 2. 构建下推请求并执行下推
            PushRequest pushRequest = PushRequest.createPurchaseOrderToInStockRequest(purchaseOrderNumbers);
            String pushResponse = pushBill(CommonConstant.ErpFormId.PURCHASE_ORDER, pushRequest);

            // 3. 解析下推响应
            PushResponse pushResult = GsonUtils.gsonToBean(pushResponse, PushResponse.class);

            if (!pushResult.isSuccess()) {
                String errorMsg = "下推失败: " + pushResult.getErrorMessages();
                logger.error("采购订单下推采购入库单失败，采购订单编码: {}, 错误信息: {}", purchaseOrderNumbers, errorMsg);
                return false;
            }

            // 4. 获取下推成功生成的采购入库单编号
            List<String> inStockNumbers = pushResult.getSuccessNumbers();
            logger.info("下推成功，生成采购入库单编号: {}", inStockNumbers);

            if (inStockNumbers.isEmpty()) {
                logger.error("采购订单下推失败: 下推成功但未获取到生成的采购入库单编号");
                return false;
            }

            // 5. 对每个生成的采购入库单执行提交和审核
            for (String inStockNumber : inStockNumbers) {
                // 5.1 提交单据
                boolean submitResult = sendSubmitSavaBill(CommonConstant.ErpFormId.PURCHASE_IN_STOCK, "采购入库单", inStockNumber, null);
                if (!submitResult) {
                    logger.error("采购订单下推失败: 采购入库单 {} 提交失败", inStockNumber);
                    return false;
                }

                // 5.2 审核单据
                boolean auditResult = sendAuditSavaBill(CommonConstant.ErpFormId.PURCHASE_IN_STOCK, "采购入库单", inStockNumber, null);
                if (!auditResult) {
                    logger.error("采购订单下推失败: 采购入库单 {} 审核失败", inStockNumber);
                    return false;
                }

                logger.info("采购入库单 {} 提交和审核成功", inStockNumber);
            }

            // 6. 全部成功
            logger.info("采购订单下推采购入库单完整流程成功，采购订单编码: {}, 生成采购入库单: {}",
                    purchaseOrderNumbers, String.join(",", inStockNumbers));
            return true;

        } catch (Exception e) {
            logger.error("采购订单下推采购入库单完整流程异常，采购订单编码: {}, 错误: {}", purchaseOrderNumbers, e.getMessage());
            return false;
        }
    }

    /**
     * 单个采购订单下推采购入库单（完整流程：下推→提交→审核）
     *
     * @param purchaseOrderNumber 采购订单编码
     * @return 下推结果：true-成功，false-失败
     */
    public boolean pushSinglePurchaseOrderToInStock(String purchaseOrderNumber) {
        logger.info("开始单个采购订单下推采购入库单完整流程，采购订单编码: {}", purchaseOrderNumber);
        if (purchaseOrderNumber == null || purchaseOrderNumber.trim().isEmpty()) {
            logger.error("采购订单下推失败: 采购订单编码不能为空");
            return false;
        }
        List<String> orderNumbers = Arrays.asList(purchaseOrderNumber);
        return pushPurchaseOrderToInStock(orderNumbers);
    }

    /**
     * 上报其他入库单
     *
     * @param materials 物料数据列表
     * @param remark    备注信息
     * @return 上报结果
     */
    public boolean reportOtherInStock(List<OtherInStockMaterialDto> materials, String remark) {
        logger.info("开始上报其他入库单，物料数量: {}", materials.size());
        String msg = "";
        String taskNo = "OTHER_IN_STOCK_" + System.currentTimeMillis();

        try {
            // 1. 构建其他入库单数据
            OtherInStockReport otherInStockReport = buildOtherInStockReport(materials, remark);

            // 2. 包装为ERP通用上报格式
            ErpTransferReportCommon erpReportCommon = ErpTransferReportCommon.getDefault(otherInStockReport);

            // 3. 登录ERP
            if (!loginErp()) {
                msg = "登录ERP失败!";
                logger.error("其他入库单上报失败: {}", msg);
                // upperSignalDataService.insertData(GsonUtils.toJsonString(erpReportCommon),
                //         "上报其他入库单失败," + msg, new Date(), "ERP", taskNo, 1);
                return false;
            }

            // 4. 保存单据
            String billNo = sendSavaBill(CommonConstant.ErpFormId.OTHER_IN_STOCK, "其他入库单",
                    GsonUtils.toJsonString(erpReportCommon), null);

            if (StringUtils.isEmpty(billNo)) {
                msg = "其他入库单保存失败!";
                logger.error("其他入库单上报失败: {}", msg);
                // upperSignalDataService.insertData(GsonUtils.toJsonString(erpReportCommon),
                //         "上报其他入库单失败," + msg, new Date(), "ERP", taskNo, 1);
                return false;
            }

            logger.info("其他入库单单据编号: {} 保存成功!", billNo);

            // 5. 提交单据
            boolean submit = sendSubmitSavaBill(CommonConstant.ErpFormId.OTHER_IN_STOCK, "其他入库单", billNo, null);
            if (!submit) {
                msg = "其他入库单单据编号:" + billNo + "提交失败!";
                logger.error("其他入库单上报失败: {}", msg);
                // upperSignalDataService.insertData("单据编号:" + billNo,
                //         "上报其他入库单失败," + msg, new Date(), "ERP", taskNo, 1);
                return false;
            }

            // 6. 审核单据
            boolean audit = sendAuditSavaBill(CommonConstant.ErpFormId.OTHER_IN_STOCK, "其他入库单", billNo, null);
            if (!audit) {
                msg = "其他入库单单据编号:" + billNo + "审核失败!";
                logger.error("其他入库单上报失败: {}", msg);
                // upperSignalDataService.insertData("单据编号:" + billNo,
                //         "上报其他入库单失败," + msg, new Date(), "ERP", taskNo, 1);
                return false;
            }

            // 7. 上报成功
            logger.info("其他入库单上报成功，单据编号: {}", billNo);
            // upperSignalDataService.insertData(GsonUtils.toJsonString(erpReportCommon),
            //         "上报其他入库单成功，单据编号:" + billNo, new Date(), "ERP", taskNo, 1);
            return true;

        } catch (Exception e) {
            msg = "其他入库单上报异常: " + e.getMessage();
            logger.error("其他入库单上报异常: {}", e.getMessage());
            // upperSignalDataService.insertData("异常信息",
            //         "上报其他入库单失败," + msg, new Date(), "ERP", taskNo, 1);
            return false;
        }
    }

    /**
     * 构建其他入库单数据
     *
     * @param materials 物料数据列表
     * @param remark    备注信息
     * @return 其他入库单数据
     */
    private OtherInStockReport buildOtherInStockReport(List<OtherInStockMaterialDto> materials, String remark) {
        OtherInStockReport report = new OtherInStockReport();

        // 设置表头信息
        report.setFBillTypeID(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_IN_STOCK_BILL_TYPE)));
        report.setFStockOrgId(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_STOCK_ORG)));
        report.setFStockDirect("GENERAL"); // 库存方向：普通
        report.setFDate(DateAndTimeUtil.getDetailTime()); // 当前日期时间
        report.setFSupplierId(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_IN_STOCK_SUPPLIER)));
        report.setFOwnerTypeIdHead(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER_TYPE));
        report.setFOwnerIdHead(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER)));
        report.setFPczoAssistant(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_IN_STOCK_TYPE)));
        report.setFPczoBase1(FNumberBig.getFNUMBERValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_IN_STOCK_PROJECT_HEAD)));
        report.setFNote(StringUtils.isNotEmpty(remark) ? remark : "");
        report.setFScanBox("");
        report.setFPczoBase(FNumberBig.getFNUMBERValue(""));
        report.setFPczoComboQtr("");

        // 构建明细信息
        List<OtherInStockReportEntry> entries = new ArrayList<>();
        for (OtherInStockMaterialDto material : materials) {
            OtherInStockReportEntry entry = buildOtherInStockReportEntry(material);
            entries.add(entry);
        }
        report.setFEntity(entries);

        return report;
    }

    /**
     * 构建其他入库单明细数据
     *
     * @param material 物料数据
     * @return 其他入库单明细数据
     */
    private OtherInStockReportEntry buildOtherInStockReportEntry(OtherInStockMaterialDto material) {
        OtherInStockReportEntry entry = new OtherInStockReportEntry();

        // 设置明细基本信息
        entry.setFMaterialId(FNumber.getFNumberValue(material.getMaterialCode()));
        entry.setFUnitId(FNumber.getFNumberValue(material.getUnitCode()));
        entry.setFQty(material.getQuantity() != null ? material.getQuantity() : BigDecimal.ZERO);

        // 设置仓库信息
        String stockCode = StringUtils.isNotEmpty(material.getStockCode()) ? material.getStockCode() :
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_IN_STOCK_DEFAULT_STOCK);
        entry.setFStockId(FNumber.getFNumberValue(stockCode));

        // 设置库存状态
        String statusCode = StringUtils.isNotEmpty(material.getStockStatusCode()) ? material.getStockStatusCode() :
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_IN_STOCK_DEFAULT_STATUS);
        entry.setFStockStatusId(FNumber.getFNumberValue(statusCode));

        // 设置批号
        if (StringUtils.isNotEmpty(material.getLotNumber())) {
            entry.setFLot(FNumber.getFNumberValue(material.getLotNumber()));
        }

        // 设置日期信息
        entry.setFProduceDate(StringUtils.isNotEmpty(material.getProduceDate()) ?
                material.getProduceDate() : DateAndTimeUtil.getDetailTime());
        entry.setFInstockDate(StringUtils.isNotEmpty(material.getInstockDate()) ?
                material.getInstockDate() : DateAndTimeUtil.getDetailTime());

        // 设置仓位信息
        if (StringUtils.isNotEmpty(material.getAreaCode()) || StringUtils.isNotEmpty(material.getPositionCode())) {
            OtherInStockReportEntry.FStockLocId stockLocId = new OtherInStockReportEntry.FStockLocId();
            if (StringUtils.isNotEmpty(material.getAreaCode())) {
                stockLocId.setFStockLocIdFf100012(FNumber.getFNumberValue(material.getAreaCode()));
            }
            if (StringUtils.isNotEmpty(material.getPositionCode())) {
                stockLocId.setFStockLocIdFf100013(FNumber.getFNumberValue(material.getPositionCode()));
            }
            entry.setFStockLocId(stockLocId);
        }

        // 设置货主和保管者信息
        entry.setFOwnerTypeId(projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER_TYPE));
        entry.setFOwnerId(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER)));
        entry.setFKeeperTypeId(projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_KEEPER_TYPE));
        entry.setFKeeperId(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_KEEPER)));

        // 设置辅助信息
        entry.setFEntryNote(StringUtils.isNotEmpty(material.getRemark()) ? material.getRemark() : "");
        entry.setFProjectNo(StringUtils.isNotEmpty(material.getProjectNo()) ? material.getProjectNo() : "");

        // 设置辅单位信息
        if (StringUtils.isNotEmpty(material.getExtAuxUnitCode())) {
            entry.setFExtAuxUnitId(FNumber.getFNumberValue(material.getExtAuxUnitCode()));
            entry.setFExtAuxUnitQty(material.getExtAuxUnitQty() != null ? material.getExtAuxUnitQty() : BigDecimal.ZERO);
        } else {
            entry.setFExtAuxUnitQty(BigDecimal.ZERO);
        }

        // 设置辅助属性
        if (StringUtils.isNotEmpty(material.getAuxPropSize())) {
            OtherInStockReportEntry.FAuxPropId auxPropId = new OtherInStockReportEntry.FAuxPropId();
            auxPropId.setFauxPropIdFf100001(FNumber.getFNumberValue(material.getAuxPropSize()));
            entry.setFAuxPropId(auxPropId);
        }

        // 设置其他默认值
        entry.setFInStockType("");
        entry.setFSrcBillNo("");
        entry.setFSrcBillTypeId("");
        entry.setFMtoNo("");

        return entry;
    }

    /**
     * 上报其他出库单
     *
     * @param materials 物料数据列表
     * @param remark    备注信息
     * @return 上报结果
     */
    public boolean reportOtherOutStock(List<OtherOutStockMaterialDto> materials, String remark) {
        logger.info("开始上报其他出库单，物料数量: {}", materials.size());
        String msg = "";
        String taskNo = "OTHER_OUT_STOCK_" + System.currentTimeMillis();

        try {
            // 1. 构建其他出库单数据
            OtherOutStockReport otherOutStockReport = buildOtherOutStockReport(materials, remark);

            // 2. 包装为ERP通用上报格式
            ErpTransferReportCommon erpReportCommon = ErpTransferReportCommon.getDefault(otherOutStockReport);

            // 3. 登录ERP
            if (!loginErp()) {
                msg = "登录ERP失败!";
                logger.error("其他出库单上报失败: {}", msg);
                // upperSignalDataService.insertData(GsonUtils.toJsonString(erpReportCommon),
                //         "上报其他出库单失败," + msg, new Date(), "ERP", taskNo, 1);
                return false;
            }

            // 4. 保存单据
            String billNo = sendSavaBill(CommonConstant.ErpFormId.OTHER_OUT_STOCK, "其他出库单",
                    GsonUtils.toJsonString(erpReportCommon), null);

            if (StringUtils.isEmpty(billNo)) {
                msg = "其他出库单保存失败!";
                logger.error("其他出库单上报失败: {}", msg);
                // upperSignalDataService.insertData(GsonUtils.toJsonString(erpReportCommon),
                //         "上报其他出库单失败," + msg, new Date(), "ERP", taskNo, 1);
                return false;
            }

            logger.info("其他出库单单据编号: {} 保存成功!", billNo);

            // 5. 提交单据
            boolean submit = sendSubmitSavaBill(CommonConstant.ErpFormId.OTHER_OUT_STOCK, "其他出库单", billNo, null);
            if (!submit) {
                msg = "其他出库单单据编号:" + billNo + "提交失败!";
                logger.error("其他出库单上报失败: {}", msg);
                // upperSignalDataService.insertData("单据编号:" + billNo,
                //         "上报其他出库单失败," + msg, new Date(), "ERP", taskNo, 1);
                return false;
            }

            // 6. 审核单据
            boolean audit = sendAuditSavaBill(CommonConstant.ErpFormId.OTHER_OUT_STOCK, "其他出库单", billNo, null);
            if (!audit) {
                msg = "其他出库单单据编号:" + billNo + "审核失败!";
                logger.error("其他出库单上报失败: {}", msg);
                // upperSignalDataService.insertData("单据编号:" + billNo,
                //         "上报其他出库单失败," + msg, new Date(), "ERP", taskNo, 1);
                return false;
            }

            // 7. 上报成功
            logger.info("其他出库单上报成功，单据编号: {}", billNo);
            // upperSignalDataService.insertData(GsonUtils.toJsonString(erpReportCommon),
            //         "上报其他出库单成功，单据编号:" + billNo, new Date(), "ERP", taskNo, 1);
            return true;

        } catch (Exception e) {
            msg = "其他出库单上报异常: " + e.getMessage();
            logger.error("其他出库单上报异常: {}", e.getMessage());
            // upperSignalDataService.insertData("异常信息",
            //         "上报其他出库单失败," + msg, new Date(), "ERP", taskNo, 1);
            return false;
        }
    }

    /**
     * 构建其他出库单数据
     *
     * @param materials 物料数据列表
     * @param remark    备注信息
     * @return 其他出库单数据
     */
    private OtherOutStockReport buildOtherOutStockReport(List<OtherOutStockMaterialDto> materials, String remark) {
        OtherOutStockReport report = new OtherOutStockReport();

        // 设置表头信息
        report.setFBillTypeID(FNumberBig.getFNUMBERValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_OUT_STOCK_BILL_TYPE)));
        report.setFStockOrgId(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_STOCK_ORG)));
        report.setFStockDirect("GENERAL"); // 库存方向：普通
        report.setFDate(DateAndTimeUtil.getDetailTime()); // 当前日期时间
        report.setFCustId(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_OUT_STOCK_CUSTOMER)));
        report.setFDeptId(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_OUT_STOCK_DEPT)));
        report.setFPickerId(OtherOutStockReport.FStaffNumber.of(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_OUT_STOCK_PICKER)));
        report.setFBizType(projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_OUT_STOCK_BIZ_TYPE));
        report.setFOwnerTypeIdHead(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER_TYPE));
        report.setFOwnerIdHead(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER)));
        report.setFPczoAssistant(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_OUT_STOCK_ASSISTANT)));
        report.setFPczoBase1(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_OUT_STOCK_PRODUCT)));
        report.setFNote(StringUtils.isNotEmpty(remark) ? remark : "");
        report.setFScanBox("");
        report.setFPczoBase(null);
        report.setFPczoComboQtr("");

        // 构建明细信息
        List<OtherOutStockReportEntry> entries = new ArrayList<>();
        for (OtherOutStockMaterialDto material : materials) {
            OtherOutStockReportEntry entry = buildOtherOutStockReportEntry(material);
            entries.add(entry);
        }
        report.setFEntity(entries);

        return report;
    }

    /**
     * 构建其他出库单明细数据
     *
     * @param material 物料数据
     * @return 其他出库单明细数据
     */
    private OtherOutStockReportEntry buildOtherOutStockReportEntry(OtherOutStockMaterialDto material) {
        OtherOutStockReportEntry entry = new OtherOutStockReportEntry();

        // 设置明细基本信息
        entry.setFMaterialId(FNumber.getFNumberValue(material.getMaterialCode()));
        entry.setFUnitId(FNumber.getFNumberValue(material.getUnitCode()));
        entry.setFQty(material.getQuantity() != null ? material.getQuantity() : BigDecimal.ZERO);

        // 设置仓库信息
        String stockCode = StringUtils.isNotEmpty(material.getStockCode()) ? material.getStockCode() :
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_OUT_STOCK_DEFAULT_STOCK);
        entry.setFStockId(FNumber.getFNumberValue(stockCode));

        // 设置库存状态
        String statusCode = StringUtils.isNotEmpty(material.getStockStatusCode()) ? material.getStockStatusCode() :
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.OTHER_OUT_STOCK_DEFAULT_STATUS);
        entry.setFStockStatusId(FNumber.getFNumberValue(statusCode));

        // 设置批号
        if (StringUtils.isNotEmpty(material.getLotNumber())) {
            entry.setFLot(FNumber.getFNumberValue(material.getLotNumber()));
        }

        // 设置日期信息
        entry.setFProduceDate(StringUtils.isNotEmpty(material.getProduceDate()) ?
                material.getProduceDate() : DateAndTimeUtil.getDetailTime());
        entry.setFInstockDate(StringUtils.isNotEmpty(material.getOutstockDate()) ?
                material.getOutstockDate() : DateAndTimeUtil.getDetailTime());

        // 设置仓位信息
        if (StringUtils.isNotEmpty(material.getAreaCode()) || StringUtils.isNotEmpty(material.getPositionCode())) {
            OtherOutStockReportEntry.FStockLocId stockLocId = new OtherOutStockReportEntry.FStockLocId();
            if (StringUtils.isNotEmpty(material.getAreaCode())) {
                stockLocId.setFStockLocIdFf100012(FNumber.getFNumberValue(material.getAreaCode()));
            }
            if (StringUtils.isNotEmpty(material.getPositionCode())) {
                stockLocId.setFStockLocIdFf100013(FNumber.getFNumberValue(material.getPositionCode()));
            }
            entry.setFStockLocId(stockLocId);
        }

        // 设置货主和保管者信息
        entry.setFOwnerTypeId(projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER_TYPE));
        entry.setFOwnerId(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER)));
        entry.setFKeeperTypeId(projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_KEEPER_TYPE));
        entry.setFKeeperId(FNumber.getFNumberValue(
                projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_KEEPER)));

        // 设置辅助信息
        entry.setFEntryNote(StringUtils.isNotEmpty(material.getRemark()) ? material.getRemark() : "");
        entry.setFProjectNo(StringUtils.isNotEmpty(material.getProjectNo()) ? material.getProjectNo() : "");

        // 设置辅单位信息
        if (StringUtils.isNotEmpty(material.getExtAuxUnitCode())) {
            entry.setFExtAuxUnitId(FNumber.getFNumberValue(material.getExtAuxUnitCode()));
            entry.setFExtAuxUnitQty(material.getExtAuxUnitQty() != null ? material.getExtAuxUnitQty() : BigDecimal.ZERO);
        } else {
            entry.setFExtAuxUnitQty(BigDecimal.ZERO);
        }

        // 设置辅助属性
        if (StringUtils.isNotEmpty(material.getAuxPropSize())) {
            OtherOutStockReportEntry.FAuxPropId auxPropId = new OtherOutStockReportEntry.FAuxPropId();
            auxPropId.setFauxPropIdFf100001(FNumber.getFNumberValue(material.getAuxPropSize()));
            entry.setFAuxPropId(auxPropId);
        }

        // 设置其他默认值
        entry.setFSrcBillNo("");
        entry.setFSrcBillTypeId("");
        entry.setFMtoNo("");

        return entry;
    }

    /**
     * 上报直接调拨单到ERP
     *
     * @param mainRecord ERP上报主表记录
     * @param details ERP上报明细记录
     * @return ERP单据编号，失败返回null
     */
    public String reportTransferBill(ErpReportMain mainRecord, List<ErpReportDetail> details) {
        String msg;
        try {
            logger.info("开始上报调拨单到ERP：单据编码：{}，明细数量：{}",
                    mainRecord.getDocumentCode(), details.size());

            // 1. 构建调拨单数据
            TransferBillReport transferReport = buildTransferBillReport(mainRecord, details);

            // 2. 包装为ERP通用上报格式
            ErpTransferReportCommon erpReportCommon = ErpTransferReportCommon.getDefault(transferReport);

            // 3. 登录ERP
            if (!loginErp()) {
                msg = "登录ERP失败!";
                logger.error("调拨单上报失败: {}", msg);
                return null;
            }

            // 4. 保存单据
            String billNo = sendSavaBill("STK_TransferDirect", "直接调拨单",
                    GsonUtils.toJsonString(erpReportCommon), null);

            if (StringUtils.isEmpty(billNo)) {
                msg = "调拨单保存失败!";
                logger.error("调拨单上报失败: {}", msg);
                return null;
            }

            logger.info("调拨单单据编号: {} 保存成功!", billNo);

            // 5. 提交单据
            boolean submit = sendSubmitSavaBill("STK_TransferDirect", "直接调拨单", billNo, null);
            if (!submit) {
                msg = "调拨单单据编号:" + billNo + "提交失败!";
                logger.error("调拨单上报失败: {}", msg);
                return null;
            }

            // 6. 审核单据
            boolean audit = sendAuditSavaBill("STK_TransferDirect", "直接调拨单", billNo, null);
            if (!audit) {
                msg = "调拨单单据编号:" + billNo + "审核失败!";
                logger.error("调拨单上报失败: {}", msg);
                return null;
            }

            // 7. 上报成功
            logger.info("调拨单上报成功，单据编号: {}", billNo);
            return billNo;

        } catch (Exception e) {
            msg = "调拨单上报异常: " + e.getMessage();
            logger.error("调拨单上报异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 上报采购入库单到ERP
     *
     * @param mainRecord ERP上报主表记录
     * @param details ERP上报明细记录
     * @return ERP单据编号，失败返回null
     */
    public String reportPurchaseInStockBill(ErpReportMain mainRecord, List<ErpReportDetail> details) {
        String msg;
        try {
            logger.info("开始上报采购入库单到ERP：单据编码：{}，明细数量：{}",
                    mainRecord.getDocumentCode(), details.size());

            // 1. 构建采购入库单数据
            PurchaseInStockReport purchaseReport = buildPurchaseInStockReport(mainRecord, details);

            // 2. 包装为ERP通用上报格式
            ErpTransferReportCommon erpReportCommon = ErpTransferReportCommon.getDefault(purchaseReport);

            // 3. 登录ERP
            if (!loginErp()) {
                msg = "登录ERP失败!";
                logger.error("采购入库单上报失败: {}", msg);
                return null;
            }

            // 4. 保存单据
            String billNo = sendSavaBill(CommonConstant.ErpFormId.PURCHASE_IN_STOCK, "采购入库单",
                    GsonUtils.toJsonString(erpReportCommon), null);

            if (StringUtils.isEmpty(billNo)) {
                msg = "采购入库单保存失败!";
                logger.error("采购入库单上报失败: {}", msg);
                return null;
            }

            logger.info("采购入库单单据编号: {} 保存成功!", billNo);

            // 5. 提交单据
            boolean submit = sendSubmitSavaBill(CommonConstant.ErpFormId.PURCHASE_IN_STOCK, "采购入库单", billNo, null);
            if (!submit) {
                msg = "采购入库单单据编号:" + billNo + "提交失败!";
                logger.error("采购入库单上报失败: {}", msg);
                return null;
            }

            // 6. 审核单据
            boolean audit = sendAuditSavaBill(CommonConstant.ErpFormId.PURCHASE_IN_STOCK, "采购入库单", billNo, null);
            if (!audit) {
                msg = "采购入库单单据编号:" + billNo + "审核失败!";
                logger.error("采购入库单上报失败: {}", msg);
                return null;
            }

            // 7. 上报成功
            logger.info("采购入库单上报成功，单据编号: {}", billNo);
            return billNo;

        } catch (Exception e) {
            msg = "采购入库单上报异常: " + e.getMessage();
            logger.error("采购入库单上报异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建调拨单数据
     */
    private TransferBillReport buildTransferBillReport(ErpReportMain mainRecord, List<ErpReportDetail> details) {
        TransferBillReport report = new TransferBillReport();

        // 设置主表信息
        report.setFBillTypeID(FNumberBig.getFNUMBERValue("ZJDB01_SYS"));
        report.setFBizType("NORMAL");
        report.setFTransferDirect("GENERAL");
        report.setFTransferBizType("InnerOrgTransfer");

        // 获取发货通知单信息
        DeliveryNotice deliveryNotice = getDeliveryNoticeByTransactionCode(mainRecord.getDocumentCode());
        String customerId = null;

        if (deliveryNotice != null) {
            customerId = deliveryNotice.getCustomerId();
        } else {
            // 如果找不到发货通知单，使用配置中的默认值
            customerId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.TRANSFER_CUSTOMER_ID);
        }

        // 供应商信息直接从配置获取
        String supplierId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.TRANSFER_SUPPLIER_ID);
        
        // 从配置中获取组织信息
        String orgId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_STOCK_ORG);
        report.setFSettleOrgId(FNumber.getFNumberValue(orgId != null ? orgId : "101"));
        report.setFSaleOrgId(FNumber.getFNumberValue(orgId != null ? orgId : "101"));
        report.setFStockOutOrgId(FNumber.getFNumberValue(orgId != null ? orgId : "101"));
        report.setFStockOrgId(FNumber.getFNumberValue(orgId != null ? orgId : "101"));
        
        // 货主信息
        String ownerType = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER_TYPE);
        String ownerId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER);
        report.setFOwnerTypeOutIdHead(ownerType != null ? ownerType : "BD_OwnerOrg");
        report.setFOwnerOutIdHead(FNumber.getFNumberValue(ownerId != null ? ownerId : "101"));
        report.setFOwnerTypeIdHead(ownerType != null ? ownerType : "BD_OwnerOrg");
        report.setFOwnerIdHead(FNumber.getFNumberValue(ownerId != null ? ownerId : "101"));
        
        // 税务和币别信息
        report.setFIsIncludedTax(true);
        report.setFIsPriceExcludeTax(true);
        report.setFExchangeTypeId(FNumberBig.getFNUMBERValue("HLTX01_SYS"));
        report.setFSettleCurrId(FNumberBig.getFNUMBERValue("PRE001"));
        report.setFExchangeRate(1.0);
        report.setFBaseCurrId(FNumber.getFNumberValue("PRE001"));
        
        // 日期信息
        report.setFDate(DateAndTimeUtil.getDetailTime());
        
        // 客户和供应商信息（从发货通知单或配置获取）
        report.setFSupplierId(FNumberBig.getFNUMBERValue(supplierId != null ? supplierId : ""));
        report.setFCustId(FNumberBig.getFNUMBERValue(customerId != null ? customerId : ""));
        
        // 按物料编码分组ErpReportDetail
        Map<String, List<ErpReportDetail>> materialGroups = details.stream()
                .collect(Collectors.groupingBy(ErpReportDetail::getMaterialCode));

        // 构建明细信息
        List<TransferBillReportEntry> entries = new ArrayList<>();
        for (Map.Entry<String, List<ErpReportDetail>> entry : materialGroups.entrySet()) {
            String materialCode = entry.getKey();
            List<ErpReportDetail> materialDetails = entry.getValue();

            // 为每个物料创建一个明细记录
            TransferBillReportEntry reportEntry = buildTransferBillEntry(materialCode, materialDetails, deliveryNotice);
            entries.add(reportEntry);
        }
        report.setFBillEntry(entries);

        return report;
    }

    /**
     * 根据单据编码获取发货通知单
     */
    private DeliveryNotice getDeliveryNoticeByTransactionCode(String transactionCode) {
        try {
            return deliveryNoticeService.lambdaQuery()
                    .eq(DeliveryNotice::getBillNo, transactionCode)
                    .one();
        } catch (Exception e) {
            logger.warn("根据单据编码查询发货通知单失败：{}，错误：{}", transactionCode, e.getMessage());
            return null;
        }
    }
    
    /**
     * 构建调拨单明细数据
     */
    private TransferBillReportEntry buildTransferBillEntry(String materialCode, List<ErpReportDetail> materialDetails, DeliveryNotice deliveryNotice) {
        TransferBillReportEntry entry = new TransferBillReportEntry();

        // 取第一个明细作为基础信息（同一物料的信息应该是一致的）
        ErpReportDetail firstDetail = materialDetails.get(0);

        // 基本信息
        entry.setFRowType("Standard");
        entry.setFMaterialId(FNumber.getFNumberValue(materialCode));

        // 单位信息（可以从物料信息中获取，这里先使用默认值）
        String unitCode = firstDetail.getUnit();
        entry.setFUnitId(FNumber.getFNumberValue(unitCode != null ? unitCode : "Pcs"));
        entry.setFBaseUnitId(FNumber.getFNumberValue(unitCode != null ? unitCode : "Pcs"));
        
        // 汇总该物料所有容器的数量
        BigDecimal totalQuantity = materialDetails.stream()
                .map(ErpReportDetail::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        entry.setFQty(totalQuantity);
        entry.setFBaseQty(totalQuantity);
        entry.setFSaleQty(totalQuantity);
        entry.setFSalBaseQty(totalQuantity);
        entry.setFPriceQty(totalQuantity);
        entry.setFPriceBaseQty(totalQuantity);

        // 批次信息（使用第一个容器的信息，或者可以考虑其他处理方式）
        String containerCode = firstDetail.getContainerCode();
        if (containerCode != null && !containerCode.isEmpty()) {
            entry.setFLot(FNumber.getFNumberValue(containerCode));
            entry.setFDestLot(FNumber.getFNumberValue(containerCode));
        }
        
        // 仓库信息（从配置中获取）
        String srcStockId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.TRANSFER_SRC_STOCK);
        String destStockId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.TRANSFER_DEST_STOCK);
        entry.setFSrcStockId(FNumber.getFNumberValue(srcStockId != null ? srcStockId : "103"));
        entry.setFDestStockId(FNumber.getFNumberValue(destStockId != null ? destStockId : "102"));

        // 库存状态
        String stockStatusId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.TRANSFER_DEFAULT_STOCK_STATUS);
        entry.setFSrcStockStatusId(FNumber.getFNumberValue(stockStatusId != null ? stockStatusId : "KCZT01_SYS"));
        entry.setFDestStockStatusId(FNumber.getFNumberValue(stockStatusId != null ? stockStatusId : "KCZT01_SYS"));

        // 库位信息（从配置中获取）
        String destStockLocArea = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.TRANSFER_DEST_STOCK_LOC_AREA);
        String destStockLocPosition = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.TRANSFER_DEST_STOCK_LOC_POSITION);
        if (destStockLocArea != null || destStockLocPosition != null) {
            TransferBillReportEntry.FDestStockLocId destStockLocId = new TransferBillReportEntry.FDestStockLocId();
            if (destStockLocArea != null) {
                destStockLocId.setFDestStockLocIdFF100012(FNumber.getFNumberValue(destStockLocArea));
            }
            if (destStockLocPosition != null) {
                destStockLocId.setFDestStockLocIdFF100013(FNumber.getFNumberValue(destStockLocPosition));
            }
            entry.setFDestStockLocId(destStockLocId);
        }
        
        // 业务日期
        entry.setFBusinessDate(DateAndTimeUtil.getDetailTime());
        
        // 货主和保管者信息
        String ownerType = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER_TYPE);
        String ownerId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_OWNER);
        String keeperType = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_KEEPER_TYPE);
        String keeperId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.COMMON_KEEPER);
        
        entry.setFOwnerTypeOutId(ownerType != null ? ownerType : "BD_OwnerOrg");
        entry.setFOwnerOutId(FNumber.getFNumberValue(ownerId != null ? ownerId : "101"));
        entry.setFOwnerTypeId(ownerType != null ? ownerType : "BD_OwnerOrg");
        entry.setFOwnerId(FNumber.getFNumberValue(ownerId != null ? ownerId : "101"));
        entry.setFKeeperTypeId(keeperType != null ? keeperType : "BD_KeeperOrg");
        entry.setFKeeperId(FNumber.getFNumberValue(keeperId != null ? keeperId : "101"));
        entry.setFKeeperTypeOutId(keeperType != null ? keeperType : "BD_KeeperOrg");
        entry.setFKeeperOutId(FNumber.getFNumberValue(keeperId != null ? keeperId : "101"));
        
        // 目标物料（与源物料相同）
        entry.setFDestMaterialId(FNumberBig.getFNUMBERValue(materialCode));
        
        // 计价单位
        entry.setFSaleUnitId(FNumber.getFNumberValue(unitCode != null ? unitCode : "Pcs"));
        entry.setFPriceUnitId(FNumber.getFNumberValue(unitCode != null ? unitCode : "Pcs"));

        // 关联关系处理
        if (deliveryNotice != null) {
            List<TransferBillReportEntry.FBillEntryLink> linkList = new ArrayList<>();
            TransferBillReportEntry.FBillEntryLink link = new TransferBillReportEntry.FBillEntryLink();

            // 设置固定的关联关系信息
            link.setFBillEntryLinkFRuleId("DeliveryNotice-StkTransferDirect");
            link.setFBillEntryLinkFSTableName("T_SAL_DELIVERYNOTICEENTRY");

            // 设置源单信息（发货通知单的ID和明细ID）
            // 由于ID是String类型，我们需要安全地转换为Integer，或者使用hashCode
            try {
                link.setFBillEntryLinkFSBillId(Integer.valueOf(deliveryNotice.getId()));
            } catch (NumberFormatException e) {
                // 如果ID不是数字，使用hashCode作为替代
                link.setFBillEntryLinkFSBillId(deliveryNotice.getId().hashCode());
            }

            // 查找对应的发货通知单明细
            DeliveryNoticeDetail noticeDetail = deliveryNoticeDetailService.getByNoticeIdAndMaterialCode(deliveryNotice.getId(), materialCode);
            if (noticeDetail != null) {
                try {
                    link.setFBillEntryLinkFSId(Integer.valueOf(noticeDetail.getId()));
                } catch (NumberFormatException e) {
                    // 如果ID不是数字，使用hashCode作为替代
                    link.setFBillEntryLinkFSId(noticeDetail.getId().hashCode());
                }

                // 设置数量信息（使用汇总后的总数量）
                Double qty = totalQuantity.doubleValue();
                link.setFBillEntryLinkFBaseQtyOld(qty);
                link.setFBillEntryLinkFBaseQty(qty);
                link.setFBillEntryLinkFSalBaseQtyOld(qty);
                link.setFBillEntryLinkFSalBaseQty(qty);
                link.setFBillEntryLinkFPriceBaseQtyOld(qty);
                link.setFBillEntryLinkFPriceBaseQty(qty);
            }

            linkList.add(link);
            entry.setFBillEntryLink(linkList);
        }

        return entry;
    }

    /**
     * 构建采购入库单数据
     */
    private PurchaseInStockReport buildPurchaseInStockReport(ErpReportMain mainRecord, List<ErpReportDetail> details) {
        PurchaseInStockReport report = new PurchaseInStockReport();

        // 设置主表信息
        report.setFBillTypeID(FNumberBig.getFNUMBERValue("RKD01_SYS"));
        report.setFBusinessType("CG");
        report.setFDate(DateAndTimeUtil.getTimeStrByDate(new Date()));

        // 设置组织信息 - 从配置中获取默认值
        String defaultOrgId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.DEFAULT_ORG_ID);
        if (StringUtils.isEmpty(defaultOrgId)) {
            defaultOrgId = "100"; // 默认组织
        }

        report.setFStockOrgId(FNumber.getFNumberValue(defaultOrgId));
        report.setFDemandOrgId(FNumber.getFNumberValue(defaultOrgId));
        report.setFPurchaseOrgId(FNumber.getFNumberValue(defaultOrgId));

        // 设置部门信息
        String defaultDeptId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.DEFAULT_DEPT_ID);
        if (StringUtils.isEmpty(defaultDeptId)) {
            defaultDeptId = "FINBM000200"; // 默认部门
        }
        report.setFPurchaseDeptId(FNumber.getFNumberValue(defaultDeptId));

        // 设置供应商信息
        String defaultSupplierId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.DEFAULT_SUPPLIER_ID);
        if (StringUtils.isEmpty(defaultSupplierId)) {
            defaultSupplierId = "FINGYS000100"; // 默认供应商
        }
        report.setFSupplierId(FNumber.getFNumberValue(defaultSupplierId));
        report.setFSupplyId(FNumber.getFNumberValue(defaultSupplierId));
        report.setFSettleId(FNumber.getFNumberValue(defaultSupplierId));
        report.setFChargeId(FNumber.getFNumberValue(defaultSupplierId));

        // 设置货主信息
        report.setFOwnerTypeIdHead("BD_OwnerOrg");
        report.setFOwnerIdHead(FNumber.getFNumberValue(defaultOrgId));

        // 设置拆单类型
        report.setFSplitBillType("A");

        // 构建财务信息
        PurchaseInStockReportFin finInfo = buildPurchaseInStockReportFin(defaultOrgId);
        report.setFInStockFin(finInfo);

        // 按物料编码分组ErpReportDetail
        Map<String, List<ErpReportDetail>> materialGroups = details.stream()
                .collect(Collectors.groupingBy(ErpReportDetail::getMaterialCode));

        // 构建明细信息
        List<PurchaseInStockReportEntry> entries = new ArrayList<>();
        for (Map.Entry<String, List<ErpReportDetail>> entry : materialGroups.entrySet()) {
            String materialCode = entry.getKey();
            List<ErpReportDetail> materialDetails = entry.getValue();

            // 为每个物料创建一个明细记录
            PurchaseInStockReportEntry reportEntry = buildPurchaseInStockEntry(materialCode, materialDetails, mainRecord.getDocumentCode());
            entries.add(reportEntry);
        }
        report.setFInStockEntry(entries);

        return report;
    }

    /**
     * 构建采购入库单财务信息
     */
    private PurchaseInStockReportFin buildPurchaseInStockReportFin(String orgId) {
        PurchaseInStockReportFin finInfo = new PurchaseInStockReportFin();

        finInfo.setFSettleOrgId(FNumber.getFNumberValue(orgId));
        finInfo.setFSettleCurrId(FNumber.getFNumberValue("PRE001")); // 人民币
        finInfo.setFIsIncludedTax(true);
        finInfo.setFPriceTimePoint("1");
        finInfo.setFLocalCurrId(FNumber.getFNumberValue("PRE001"));
        finInfo.setFExchangeTypeId(FNumber.getFNumberValue("HLTX01_SYS"));
        finInfo.setFExchangeRate(BigDecimal.ONE);
        finInfo.setFIsPriceExcludeTax(true);
        finInfo.setFHSExchangeRate(BigDecimal.ONE);

        return finInfo;
    }

    /**
     * 构建采购入库单明细数据
     */
    private PurchaseInStockReportEntry buildPurchaseInStockEntry(String materialCode, List<ErpReportDetail> materialDetails, String documentCode) {
        PurchaseInStockReportEntry entry = new PurchaseInStockReportEntry();

        // 取第一个明细作为基础信息（同一物料的信息应该是一致的）
        ErpReportDetail firstDetail = materialDetails.get(0);

        // 基本信息
        entry.setFRowType("Standard");
        entry.setFMaterialId(FNumber.getFNumberValue(materialCode));

        // 单位信息
        String unitCode = firstDetail.getUnit();
        entry.setFUnitID(FNumber.getFNumberValue(unitCode != null ? unitCode : "Pcs"));
        entry.setFPriceUnitID(FNumber.getFNumberValue(unitCode != null ? unitCode : "Pcs"));
        entry.setFRemainInStockUnitId(FNumber.getFNumberValue(unitCode != null ? unitCode : "Pcs"));

        // 物料说明
        entry.setFMaterialDesc(firstDetail.getMaterialName() != null ? firstDetail.getMaterialName() : "");

        // 汇总该物料所有容器的数量
        BigDecimal totalQty = materialDetails.stream()
                .map(ErpReportDetail::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        entry.setFRealQty(totalQty);
        entry.setFRemainInStockQty(totalQty);
        entry.setFRemainInStockBaseQty(totalQty);
        entry.setFPriceBaseQty(totalQty);
        entry.setFAPNotJoinQty(totalQty);

        // 仓库信息 - 从配置获取默认仓库
        String defaultStockId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.DEFAULT_STOCK_ID);
        if (StringUtils.isEmpty(defaultStockId)) {
            defaultStockId = "FINCK000200"; // 默认仓库
        }
        entry.setFStockId(FNumber.getFNumberValue(defaultStockId));

        // 库存状态
        entry.setFStockStatusId(FNumber.getFNumberValue("KCZT01_SYS"));

        // 货主信息
        entry.setFOwnerTypeId("BD_OwnerOrg");
        String defaultOrgId = projectSysConfigService.getSysConfigValByType(CommonConstant.ProjectSysConfig.DEFAULT_ORG_ID);
        if (StringUtils.isEmpty(defaultOrgId)) {
            defaultOrgId = "100";
        }
        entry.setFOwnerId(FNumber.getFNumberValue(defaultOrgId));

        // 税率
        entry.setFEntryTaxRate(new BigDecimal("13.00"));

        // 构建关联关系
        buildPurchaseInStockEntryLink(entry, materialCode, documentCode, totalQty);

        return entry;
    }

    /**
     * 构建采购入库单明细关联关系
     */
    private void buildPurchaseInStockEntryLink(PurchaseInStockReportEntry entry, String materialCode, String documentCode, BigDecimal totalQty) {
        try {
            // 根据单据编码查找收料通知单
            ReceiveNotice receiveNotice = receiveNoticeService.getByBillNo(documentCode);
            if (receiveNotice == null) {
                logger.warn("未找到收料通知单，单据编码：{}", documentCode);
                return;
            }

            // 查找对应的收料通知单明细
            ReceiveNoticeDetail receiveNoticeDetail = receiveNoticeDetailService.getByNoticeIdAndMaterialId(receiveNotice.getId(), materialCode);
            if (receiveNoticeDetail == null) {
                logger.warn("未找到收料通知单明细，收料通知单ID：{}，物料编码：{}", receiveNotice.getId(), materialCode);
                return;
            }

            // 构建关联关系
            List<FInStockEntryLink> linkList = new ArrayList<>();
            FInStockEntryLink link = new FInStockEntryLink();

            // 设置固定的关联关系信息
            link.setFInStockEntryLinkFSTableName("T_PUR_RECEIVEBILLENTRY");

            // 设置源单信息（收料通知单的ID和明细ID）
            try {
                link.setFInStockEntryLinkFSBillId(receiveNotice.getId());
                link.setFInStockEntryLinkFSId(receiveNoticeDetail.getId());
            } catch (Exception e) {
                logger.warn("设置关联关系ID失败：{}", e.getMessage());
                return;
            }

            // 设置数量信息
            BigDecimal qty = totalQty;
            link.setFInStockEntryLinkFRemainInStockBaseQtyOld(qty);
            link.setFInStockEntryLinkFRemainInStockBaseQty(qty);
            link.setFInStockEntryLinkFBaseUnitQtyOld(qty);
            link.setFInStockEntryLinkFBaseUnitQty(qty);

            linkList.add(link);
            entry.setFInStockEntryLink(linkList);

            logger.debug("成功构建采购入库单关联关系，物料：{}，数量：{}", materialCode, qty);

        } catch (Exception e) {
            logger.error("构建采购入库单关联关系失败，物料：{}，错误：{}", materialCode, e.getMessage());
        }
    }



}