package com.ruoyi.vo.erp.purchasestock;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.vo.erp.common.FNumber;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购入库单关联关系实体
 * <AUTHOR>
 */
@Data
public class FInStockEntryLink {

    /**
     * 业务流程图
     */
    @JsonProperty("FInStockEntry_Link_FFlowId")
    private FNumber fInStockEntryLinkFFlowId;

    /**
     * 推进路线
     */
    @JsonProperty("FInStockEntry_Link_FFlowLineId")
    private FNumber fInStockEntryLinkFFlowLineId;

    /**
     * 转换规则
     */
    @JsonProperty("FInStockEntry_Link_FRuleId")
    private FNumber fInStockEntryLinkFRuleId;

    /**
     * 源单表内码
     */
    @JsonProperty("FInStockEntry_Link_FSTableId")
    private String fInStockEntryLinkFSTableId;

    /**
     * 源单表
     */
    @JsonProperty("FInStockEntry_Link_FSTableName")
    private String fInStockEntryLinkFSTableName;

    /**
     * 源单内码
     */
    @JsonProperty("FInStockEntry_Link_FSBillId")
    private String fInStockEntryLinkFSBillId;

    /**
     * 源单分录内码
     */
    @JsonProperty("FInStockEntry_Link_FSId")
    private String fInStockEntryLinkFSId;

    /**
     * 原始携带量
     */
    @JsonProperty("FInStockEntry_Link_FRemainInStockBaseQtyOld")
    private BigDecimal fInStockEntryLinkFRemainInStockBaseQtyOld;

    /**
     * 修改携带量
     */
    @JsonProperty("FInStockEntry_Link_FRemainInStockBaseQty")
    private BigDecimal fInStockEntryLinkFRemainInStockBaseQty;

    /**
     * 原始携带量
     */
    @JsonProperty("FInStockEntry_Link_FBaseUnitQtyOld")
    private BigDecimal fInStockEntryLinkFBaseUnitQtyOld;

    /**
     * 修改携带量
     */
    @JsonProperty("FInStockEntry_Link_FBaseUnitQty")
    private BigDecimal fInStockEntryLinkFBaseUnitQty;

    /**
     * 迁移图
     */
    @JsonProperty("FInStockEntry_Link_FLnk1TrackerId")
    private FNumber fInStockEntryLinkFLnk1TrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FInStockEntry_Link_FLnk1SState")
    private String fInStockEntryLinkFLnk1SState;

    /**
     * 数量FLnk1
     */
    @JsonProperty("FInStockEntry_Link_FLnk1Amount")
    private BigDecimal fInStockEntryLinkFLnk1Amount;

    /**
     * 迁移图
     */
    @JsonProperty("FInStockEntry_Link_FLnkTrackerId")
    private FNumber fInStockEntryLinkFLnkTrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FInStockEntry_Link_FLnkSState")
    private String fInStockEntryLinkFLnkSState;

    /**
     * 数量FLnk
     */
    @JsonProperty("FInStockEntry_Link_FLnkAmount")
    private BigDecimal fInStockEntryLinkFLnkAmount;

    /**
     * 迁移图
     */
    @JsonProperty("FInStockEntry_Link_FLnk2TrackerId")
    private FNumber fInStockEntryLinkFLnk2TrackerId;

    /**
     * 上游状态
     */
    @JsonProperty("FInStockEntry_Link_FLnk2SState")
    private String fInStockEntryLinkFLnk2SState;

    /**
     * 数量FLnk2
     */
    @JsonProperty("FInStockEntry_Link_FLnk2Amount")
    private BigDecimal fInStockEntryLinkFLnk2Amount;
}
